/**
 * Memory Cache Provider Implementation
 * 
 * High-performance in-memory cache implementation using Map data structure
 * with TTL support, automatic cleanup, and memory management. This cache
 * is ideal for development environments and single-instance deployments.
 * 
 * Key Features:
 * - TTL-based expiration with automatic cleanup
 * - Memory usage monitoring and limits
 * - Pattern-based key operations
 * - Tag-based cache invalidation
 * - Performance statistics tracking
 */

import type {
  CacheProvider,
  CacheEntry,
  CacheStats,
  CacheEvent,
  CacheStrategy
} from '../../types/cache'

/**
 * Memory Cache Provider Class
 * 
 * Implements the CacheProvider interface using in-memory storage
 * with automatic expiration, memory management, and performance tracking.
 */
export class MemoryCacheProvider implements CacheProvider {
  readonly name = 'memory'
  readonly strategy: CacheStrategy = 'memory'

  private cache = new Map<string, CacheEntry>()
  private tags = new Map<string, Set<string>>() // tag -> set of keys
  private keyTags = new Map<string, Set<string>>() // key -> set of tags
  private _stats: CacheStats
  private gcInterval: NodeJS.Timeout | null = null
  private maxSize: number
  private maxEntries: number

  constructor(options: {
    maxSize?: number // in bytes
    maxEntries?: number
    gcInterval?: number // in milliseconds
  } = {}) {
    this.maxSize = options.maxSize || 100 * 1024 * 1024 // 100MB default
    this.maxEntries = options.maxEntries || 10000
    
    this._stats = {
      hits: 0,
      misses: 0,
      hitRate: 0,
      size: 0,
      maxSize: this.maxSize,
      entryCount: 0,
      maxEntries: this.maxEntries,
      averageGetTime: 0,
      averageSetTime: 0,
      expiredEntries: 0,
      evictedEntries: 0,
      resetAt: Date.now()
    }

    // Start garbage collection
    this.startGarbageCollection(options.gcInterval || 60000) // 1 minute default
  }

  /**
   * Get value from cache
   * 
   * Retrieves a value from the cache, checking for expiration
   * and updating access statistics.
   * 
   * @param key - Cache key to retrieve
   * @returns Cached value or null if not found/expired
   */
  async get<T>(key: string): Promise<T | null> {
    const startTime = performance.now()
    
    try {
      const entry = this.cache.get(key)
      
      if (!entry) {
        this._stats.misses++
        return null
      }

      // Check expiration
      if (this.isExpired(entry)) {
        this.cache.delete(key)
        this.removeKeyFromTags(key)
        this._stats.misses++
        this._stats.expiredEntries++
        return null
      }

      this._stats.hits++
      return entry.data as T
      
    } finally {
      const duration = performance.now() - startTime
      this.updateAverageTime('get', duration)
      this.updateHitRate()
    }
  }

  /**
   * Set value in cache
   * 
   * Stores a value in the cache with optional TTL,
   * managing memory limits and updating statistics.
   * 
   * @param key - Cache key
   * @param value - Value to cache
   * @param ttl - Time to live in seconds
   */
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    const startTime = performance.now()
    
    try {
      // Check if we need to evict entries
      await this.ensureCapacity()

      const now = Date.now()
      const entryTtl = (ttl || 3600) * 1000 // Convert to milliseconds
      
      const entry: CacheEntry<T> = {
        data: value,
        key,
        timestamp: now,
        ttl: entryTtl,
        expiresAt: now + entryTtl,
        size: this.estimateSize(value)
      }

      // Remove old entry if exists
      if (this.cache.has(key)) {
        this.removeKeyFromTags(key)
      }

      this.cache.set(key, entry)
      this.updateStats()
      
    } finally {
      const duration = performance.now() - startTime
      this.updateAverageTime('set', duration)
    }
  }

  /**
   * Delete value from cache
   * 
   * Removes a specific key from the cache and cleans up
   * associated tags and statistics.
   * 
   * @param key - Cache key to delete
   * @returns True if key was deleted, false if not found
   */
  async delete(key: string): Promise<boolean> {
    const existed = this.cache.has(key)
    
    if (existed) {
      this.cache.delete(key)
      this.removeKeyFromTags(key)
      this.updateStats()
    }
    
    return existed
  }

  /**
   * Check if key exists in cache
   * 
   * Verifies if a key exists and is not expired.
   * 
   * @param key - Cache key to check
   * @returns True if key exists and is valid
   */
  async exists(key: string): Promise<boolean> {
    const entry = this.cache.get(key)
    
    if (!entry) return false
    
    if (this.isExpired(entry)) {
      this.cache.delete(key)
      this.removeKeyFromTags(key)
      this._stats.expiredEntries++
      return false
    }
    
    return true
  }

  /**
   * Get multiple values from cache
   * 
   * Efficiently retrieves multiple values in a single operation.
   * 
   * @param keys - Array of cache keys
   * @returns Array of values (null for missing/expired keys)
   */
  async getMany<T>(keys: string[]): Promise<(T | null)[]> {
    return Promise.all(keys.map(key => this.get<T>(key)))
  }

  /**
   * Set multiple values in cache
   * 
   * Efficiently stores multiple key-value pairs in a single operation.
   * 
   * @param entries - Array of cache entries to set
   */
  async setMany<T>(entries: Array<{ key: string; value: T; ttl?: number }>): Promise<void> {
    await Promise.all(entries.map(entry => this.set(entry.key, entry.value, entry.ttl)))
  }

  /**
   * Delete multiple keys from cache
   * 
   * Removes multiple keys in a single operation.
   * 
   * @param keys - Array of keys to delete
   * @returns Number of keys actually deleted
   */
  async deleteMany(keys: string[]): Promise<number> {
    let deleted = 0
    for (const key of keys) {
      if (await this.delete(key)) {
        deleted++
      }
    }
    return deleted
  }

  /**
   * Delete keys by pattern
   * 
   * Removes all keys matching a glob-style pattern.
   * 
   * @param pattern - Glob pattern to match keys
   * @returns Number of keys deleted
   */
  async deleteByPattern(pattern: string): Promise<number> {
    const regex = this.globToRegex(pattern)
    const keysToDelete: string[] = []
    
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        keysToDelete.push(key)
      }
    }
    
    return this.deleteMany(keysToDelete)
  }

  /**
   * Get keys by pattern
   * 
   * Returns all keys matching a glob-style pattern.
   * 
   * @param pattern - Glob pattern to match keys
   * @returns Array of matching keys
   */
  async getKeysByPattern(pattern: string): Promise<string[]> {
    const regex = this.globToRegex(pattern)
    const matchingKeys: string[] = []
    
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        matchingKeys.push(key)
      }
    }
    
    return matchingKeys
  }

  /**
   * Get values by tag
   * 
   * Retrieves all cached values associated with a specific tag.
   * 
   * @param tag - Tag to search for
   * @returns Array of values with the specified tag
   */
  async getByTag<T>(tag: string): Promise<T[]> {
    const keys = this.tags.get(tag)
    if (!keys) return []
    
    const values: T[] = []
    for (const key of keys) {
      const value = await this.get<T>(key)
      if (value !== null) {
        values.push(value)
      }
    }
    
    return values
  }

  /**
   * Delete values by tag
   * 
   * Removes all cached values associated with a specific tag.
   * 
   * @param tag - Tag to delete
   * @returns Number of entries deleted
   */
  async deleteByTag(tag: string): Promise<number> {
    const keys = this.tags.get(tag)
    if (!keys) return 0
    
    const keysArray = Array.from(keys)
    return this.deleteMany(keysArray)
  }

  /**
   * Tag a key
   * 
   * Associates a key with one or more tags for group operations.
   * 
   * @param key - Cache key to tag
   * @param tags - Array of tags to associate with the key
   */
  async tagKey(key: string, tags: string[]): Promise<void> {
    // Remove existing tags for this key
    this.removeKeyFromTags(key)
    
    // Add new tags
    for (const tag of tags) {
      if (!this.tags.has(tag)) {
        this.tags.set(tag, new Set())
      }
      this.tags.get(tag)!.add(key)
      
      if (!this.keyTags.has(key)) {
        this.keyTags.set(key, new Set())
      }
      this.keyTags.get(key)!.add(tag)
    }
  }

  /**
   * Clear all cache entries
   * 
   * Removes all cached data and resets statistics.
   */
  async clear(): Promise<void> {
    this.cache.clear()
    this.tags.clear()
    this.keyTags.clear()
    this.resetStats()
  }

  /**
   * Get cache size
   * 
   * Returns the current number of entries in the cache.
   * 
   * @returns Number of cache entries
   */
  async size(): Promise<number> {
    return this.cache.size
  }

  /**
   * Get cache statistics
   * 
   * Returns comprehensive statistics about cache performance and usage.
   * 
   * @returns Cache statistics object
   */
  async stats(): Promise<CacheStats> {
    this.updateStats()
    return { ...this._stats }
  }

  /**
   * Connect to cache (no-op for memory cache)
   */
  async connect(): Promise<void> {
    // No connection needed for memory cache
  }

  /**
   * Disconnect from cache
   * 
   * Stops garbage collection and clears all data.
   */
  async disconnect(): Promise<void> {
    if (this.gcInterval) {
      clearInterval(this.gcInterval)
      this.gcInterval = null
    }
    await this.clear()
  }

  // Private helper methods

  private isExpired(entry: CacheEntry): boolean {
    return Date.now() > entry.expiresAt
  }

  private removeKeyFromTags(key: string): void {
    const keyTagSet = this.keyTags.get(key)
    if (keyTagSet) {
      for (const tag of keyTagSet) {
        const tagKeySet = this.tags.get(tag)
        if (tagKeySet) {
          tagKeySet.delete(key)
          if (tagKeySet.size === 0) {
            this.tags.delete(tag)
          }
        }
      }
      this.keyTags.delete(key)
    }
  }

  private estimateSize(value: any): number {
    try {
      return JSON.stringify(value).length * 2 // Rough estimate (UTF-16)
    } catch {
      return 1000 // Fallback estimate
    }
  }

  private updateStats(): void {
    this._stats.entryCount = this.cache.size
    this._stats.size = Array.from(this.cache.values())
      .reduce((total, entry) => total + (entry.size || 0), 0)
  }

  private updateHitRate(): void {
    const total = this._stats.hits + this._stats.misses
    this._stats.hitRate = total > 0 ? this._stats.hits / total : 0
  }

  private updateAverageTime(operation: 'get' | 'set', duration: number): void {
    const key = operation === 'get' ? 'averageGetTime' : 'averageSetTime'
    this._stats[key] = (this._stats[key] + duration) / 2
  }

  private resetStats(): void {
    this._stats = {
      ...this._stats,
      hits: 0,
      misses: 0,
      hitRate: 0,
      size: 0,
      entryCount: 0,
      expiredEntries: 0,
      evictedEntries: 0,
      resetAt: Date.now()
    }
  }

  private async ensureCapacity(): Promise<void> {
    // Check entry count limit
    if (this.cache.size >= this.maxEntries) {
      await this.evictLRU(Math.floor(this.maxEntries * 0.1)) // Evict 10%
    }

    // Check memory limit
    if (this._stats.size >= this.maxSize) {
      await this.evictLRU(Math.floor(this.cache.size * 0.1)) // Evict 10%
    }
  }

  private async evictLRU(count: number): Promise<void> {
    const entries = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => a.timestamp - b.timestamp)
      .slice(0, count)

    for (const [key] of entries) {
      await this.delete(key)
      this._stats.evictedEntries++
    }
  }

  private startGarbageCollection(interval: number): void {
    this.gcInterval = setInterval(() => {
      this.cleanupExpired()
    }, interval)
  }

  private cleanupExpired(): void {
    const now = Date.now()
    const expiredKeys: string[] = []

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        expiredKeys.push(key)
      }
    }

    for (const key of expiredKeys) {
      this.cache.delete(key)
      this.removeKeyFromTags(key)
      this._stats.expiredEntries++
    }

    this.updateStats()
  }

  private globToRegex(pattern: string): RegExp {
    const escaped = pattern
      .replace(/[.+^${}()|[\]\\]/g, '\\$&') // Escape regex special chars
      .replace(/\*/g, '.*') // Convert * to .*
      .replace(/\?/g, '.') // Convert ? to .
    
    return new RegExp(`^${escaped}$`)
  }
}

// Export default instance
export default new MemoryCacheProvider()
