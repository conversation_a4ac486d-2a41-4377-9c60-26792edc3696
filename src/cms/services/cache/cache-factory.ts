/**
 * Cache Service Factory
 * 
 * This module provides factory functions and singleton management for
 * creating and configuring cache service instances. It handles the
 * instantiation of cache services with proper configuration and
 * provides a default singleton instance for application-wide use.
 * 
 * The factory pattern allows for flexible cache service creation
 * with different configurations while maintaining a consistent
 * interface and supporting dependency injection patterns.
 * 
 * Key Features:
 * - Factory function for creating configured cache service instances
 * - Singleton instance management for application-wide cache access
 * - Configuration validation and error handling
 * - Support for different cache strategies and providers
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */

import type {
  CacheService,
  CacheConfig
} from '../../types/cache'

import { CacheServiceImpl } from './cache-service'
import { defaultCacheConfig } from './cache-config'

/**
 * Create cache service instance
 * 
 * Factory function to create a configured cache service instance.
 * This function validates the configuration, initializes the
 * appropriate cache provider, and returns a fully configured
 * cache service ready for use.
 * 
 * The factory function provides a clean separation between
 * configuration and instantiation, making it easier to test
 * and configure different cache strategies.
 * 
 * @param config - Cache configuration object
 * @returns Configured cache service instance
 * @throws {Error} When configuration is invalid or provider initialization fails
 * 
 * @example
 * ```typescript
 * // Create with custom configuration
 * const customConfig = {
 *   strategy: 'memory',
 *   ttl: { content: 7200, contentList: 3600 },
 *   memory: { maxSize: 200 * 1024 * 1024 }
 * }
 * const cacheService = createCacheService(customConfig)
 * 
 * // Create with default configuration
 * const defaultService = createCacheService(defaultCacheConfig)
 * ```
 */
export function createCacheService(config: CacheConfig): CacheService {
  // Validate configuration
  if (!config) {
    throw new Error('Cache configuration is required')
  }

  if (!config.strategy) {
    throw new Error('Cache strategy must be specified')
  }

  if (!config.ttl) {
    throw new Error('Cache TTL configuration is required')
  }

  try {
    // Create and return the cache service instance
    return new CacheServiceImpl(config)
  } catch (error) {
    throw new Error(`Failed to create cache service: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Default cache service singleton
 * 
 * Pre-configured cache service instance using the default configuration.
 * This singleton provides convenient access to caching functionality
 * throughout the application without requiring manual instantiation.
 * 
 * The singleton is created lazily and uses the default configuration
 * which is optimized for typical CMS usage patterns. For custom
 * configurations, use the createCacheService factory function.
 * 
 * @example
 * ```typescript
 * import { cacheService } from './cache-factory'
 * 
 * // Use the default cache service
 * await cacheService.cacheContent('blog', 'my-post', 'en', content)
 * const cached = await cacheService.getCachedContent('blog', 'my-post', 'en')
 * ```
 */
export const cacheService = createCacheService(defaultCacheConfig)

/**
 * Create cache service with memory strategy
 * 
 * Convenience factory function for creating a cache service
 * specifically configured for memory-based caching. This is
 * useful for development, testing, or single-instance deployments.
 * 
 * @param options - Memory cache specific options
 * @returns Cache service configured for memory caching
 * 
 * @example
 * ```typescript
 * const memoryCache = createMemoryCacheService({
 *   maxSize: 50 * 1024 * 1024, // 50MB
 *   gcInterval: 30000 // 30 seconds
 * })
 * ```
 */
export function createMemoryCacheService(options?: {
  maxSize?: number
  gcInterval?: number
  ttl?: {
    content?: number
    contentList?: number
    metadata?: number
    seo?: number
    sitemap?: number
    rss?: number
  }
}): CacheService {
  const config: CacheConfig = {
    strategy: 'memory',
    ttl: {
      content: options?.ttl?.content ?? 3600,
      contentList: options?.ttl?.contentList ?? 1800,
      metadata: options?.ttl?.metadata ?? 7200,
      seo: options?.ttl?.seo ?? 3600,
      sitemap: options?.ttl?.sitemap ?? 86400,
      rss: options?.ttl?.rss ?? 3600
    },
    maxSize: options?.maxSize ?? 100 * 1024 * 1024,
    maxEntries: 10000,
    memory: {
      maxSize: options?.maxSize ?? 100 * 1024 * 1024,
      gcInterval: options?.gcInterval ?? 60000
    },
    invalidation: {
      enabled: true,
      patterns: ['content:*', 'list:*', 'seo:*'],
      onContentChange: true,
      onBuild: true
    }
  }

  return createCacheService(config)
}

/**
 * Cache service factory with validation
 * 
 * Enhanced factory function that performs comprehensive validation
 * of the cache configuration before creating the service instance.
 * This function provides detailed error messages for configuration
 * issues and ensures that the cache service is properly initialized.
 * 
 * @param config - Cache configuration to validate and use
 * @returns Validated and configured cache service instance
 * @throws {Error} With detailed validation error messages
 * 
 * @example
 * ```typescript
 * try {
 *   const cacheService = createValidatedCacheService(config)
 *   await cacheService.connect()
 * } catch (error) {
 *   console.error('Cache service creation failed:', error.message)
 * }
 * ```
 */
export function createValidatedCacheService(config: CacheConfig): CacheService {
  // Comprehensive configuration validation
  const errors: string[] = []

  if (!config) {
    errors.push('Configuration object is required')
  } else {
    if (!config.strategy) {
      errors.push('Cache strategy is required')
    } else if (!['memory', 'redis', 'file', 'hybrid'].includes(config.strategy)) {
      errors.push(`Invalid cache strategy: ${config.strategy}`)
    }

    if (!config.ttl) {
      errors.push('TTL configuration is required')
    } else {
      if (typeof config.ttl.content !== 'number' || config.ttl.content <= 0) {
        errors.push('Content TTL must be a positive number')
      }
      if (typeof config.ttl.contentList !== 'number' || config.ttl.contentList <= 0) {
        errors.push('Content list TTL must be a positive number')
      }
    }

    if (config.strategy === 'memory' && !config.memory) {
      errors.push('Memory configuration is required for memory strategy')
    }
  }

  if (errors.length > 0) {
    throw new Error(`Cache configuration validation failed:\n${errors.join('\n')}`)
  }

  return createCacheService(config)
}
