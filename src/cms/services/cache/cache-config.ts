/**
 * Cache Configuration
 * 
 * This module provides default cache configuration settings and
 * configuration presets for different deployment scenarios.
 * The configurations are optimized for typical CMS usage patterns
 * and provide sensible defaults for various cache strategies.
 * 
 * The configuration system supports multiple cache backends
 * and provides fine-grained control over TTL settings,
 * invalidation patterns, and performance parameters.
 * 
 * Key Features:
 * - Default configuration optimized for CMS workloads
 * - Environment-specific configuration presets
 * - Comprehensive TTL settings for different content types
 * - Cache invalidation pattern configuration
 * - Memory management and performance tuning options
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */

import type { CacheConfig } from '../../types/cache'

/**
 * Default Cache Configuration
 * 
 * Provides a comprehensive default configuration for the cache service
 * that is optimized for typical CMS usage patterns. This configuration
 * uses memory-based caching with reasonable TTL values and automatic
 * invalidation patterns.
 * 
 * The default configuration is designed to:
 * - Provide good performance for most CMS workloads
 * - Use conservative memory limits to prevent OOM issues
 * - Enable automatic cache invalidation for content changes
 * - Support efficient garbage collection and cleanup
 * 
 * TTL Strategy:
 * - Content: 1 hour (frequently accessed, moderate change rate)
 * - Content Lists: 30 minutes (more dynamic, affected by new content)
 * - Metadata: 2 hours (rarely changes, expensive to compute)
 * - SEO Data: 1 hour (moderate change rate, important for performance)
 * - Sitemap: 24 hours (changes infrequently, expensive to generate)
 * - RSS: 1 hour (moderate update frequency)
 * 
 * @example
 * ```typescript
 * import { defaultCacheConfig } from './cache-config'
 * 
 * const cacheService = createCacheService(defaultCacheConfig)
 * ```
 */
export const defaultCacheConfig: CacheConfig = {
  // Cache strategy - using memory for simplicity and performance
  strategy: 'memory',
  
  // Time-to-live settings for different content types (in seconds)
  ttl: {
    content: 3600,      // 1 hour - individual content items
    contentList: 1800,  // 30 minutes - lists of content (more dynamic)
    metadata: 7200,     // 2 hours - computed metadata (expensive to generate)
    seo: 3600,          // 1 hour - SEO data (moderate change frequency)
    sitemap: 86400,     // 24 hours - sitemap data (changes infrequently)
    rss: 3600           // 1 hour - RSS feed data
  },
  
  // Global cache limits
  maxSize: 100 * 1024 * 1024,  // 100MB total cache size limit
  maxEntries: 10000,           // Maximum number of cache entries
  
  // Memory-specific configuration
  memory: {
    maxSize: 100 * 1024 * 1024,  // 100MB memory limit
    gcInterval: 60000            // Garbage collection every 1 minute
  },
  
  // Cache invalidation configuration
  invalidation: {
    enabled: true,                              // Enable automatic invalidation
    patterns: ['content:*', 'list:*', 'seo:*'], // Patterns to invalidate
    onContentChange: true,                      // Invalidate when content changes
    onBuild: true                              // Invalidate on build/deploy
  }
}

/**
 * Development Cache Configuration
 * 
 * Optimized configuration for development environments with
 * shorter TTL values for faster content updates and smaller
 * memory limits to reduce resource usage during development.
 * 
 * @example
 * ```typescript
 * import { developmentCacheConfig } from './cache-config'
 * 
 * const devCacheService = createCacheService(developmentCacheConfig)
 * ```
 */
export const developmentCacheConfig: CacheConfig = {
  strategy: 'memory',
  
  // Shorter TTL for development (faster content updates)
  ttl: {
    content: 300,       // 5 minutes
    contentList: 180,   // 3 minutes
    metadata: 600,      // 10 minutes
    seo: 300,           // 5 minutes
    sitemap: 1800,      // 30 minutes
    rss: 300            // 5 minutes
  },
  
  // Smaller limits for development
  maxSize: 50 * 1024 * 1024,   // 50MB
  maxEntries: 5000,
  
  memory: {
    maxSize: 50 * 1024 * 1024,
    gcInterval: 30000           // More frequent GC (30 seconds)
  },
  
  invalidation: {
    enabled: true,
    patterns: ['content:*', 'list:*', 'seo:*'],
    onContentChange: true,
    onBuild: true
  }
}

/**
 * Production Cache Configuration
 * 
 * Optimized configuration for production environments with
 * longer TTL values for better performance and larger memory
 * limits to handle higher traffic loads.
 * 
 * @example
 * ```typescript
 * import { productionCacheConfig } from './cache-config'
 * 
 * const prodCacheService = createCacheService(productionCacheConfig)
 * ```
 */
export const productionCacheConfig: CacheConfig = {
  strategy: 'memory',
  
  // Longer TTL for production (better performance)
  ttl: {
    content: 7200,      // 2 hours
    contentList: 3600,  // 1 hour
    metadata: 14400,    // 4 hours
    seo: 7200,          // 2 hours
    sitemap: 172800,    // 48 hours
    rss: 7200           // 2 hours
  },
  
  // Larger limits for production
  maxSize: 500 * 1024 * 1024,  // 500MB
  maxEntries: 50000,
  
  memory: {
    maxSize: 500 * 1024 * 1024,
    gcInterval: 300000          // Less frequent GC (5 minutes)
  },
  
  invalidation: {
    enabled: true,
    patterns: ['content:*', 'list:*', 'seo:*'],
    onContentChange: true,
    onBuild: true
  }
}

/**
 * Testing Cache Configuration
 * 
 * Minimal configuration for testing environments with
 * very short TTL values and small memory limits to
 * ensure tests run quickly and don't interfere with
 * each other.
 * 
 * @example
 * ```typescript
 * import { testingCacheConfig } from './cache-config'
 * 
 * const testCacheService = createCacheService(testingCacheConfig)
 * ```
 */
export const testingCacheConfig: CacheConfig = {
  strategy: 'memory',
  
  // Very short TTL for testing
  ttl: {
    content: 60,        // 1 minute
    contentList: 30,    // 30 seconds
    metadata: 120,      // 2 minutes
    seo: 60,            // 1 minute
    sitemap: 300,       // 5 minutes
    rss: 60             // 1 minute
  },
  
  // Minimal limits for testing
  maxSize: 10 * 1024 * 1024,   // 10MB
  maxEntries: 1000,
  
  memory: {
    maxSize: 10 * 1024 * 1024,
    gcInterval: 10000           // Frequent GC (10 seconds)
  },
  
  invalidation: {
    enabled: true,
    patterns: ['content:*', 'list:*', 'seo:*'],
    onContentChange: true,
    onBuild: true
  }
}

/**
 * Get cache configuration by environment
 * 
 * Utility function to get the appropriate cache configuration
 * based on the current environment. This provides a convenient
 * way to automatically select the right configuration without
 * manual environment checking.
 * 
 * @param environment - The environment name ('development', 'production', 'test')
 * @returns Appropriate cache configuration for the environment
 * 
 * @example
 * ```typescript
 * const config = getCacheConfigByEnvironment(process.env.NODE_ENV)
 * const cacheService = createCacheService(config)
 * ```
 */
export function getCacheConfigByEnvironment(environment?: string): CacheConfig {
  switch (environment) {
    case 'development':
      return developmentCacheConfig
    case 'production':
      return productionCacheConfig
    case 'test':
    case 'testing':
      return testingCacheConfig
    default:
      return defaultCacheConfig
  }
}

/**
 * Create custom cache configuration
 * 
 * Utility function to create a custom cache configuration by
 * merging user-provided options with the default configuration.
 * This allows for easy customization while maintaining sensible defaults.
 * 
 * @param overrides - Configuration overrides to apply
 * @returns Custom cache configuration
 * 
 * @example
 * ```typescript
 * const customConfig = createCustomCacheConfig({
 *   ttl: { content: 7200 }, // Override content TTL to 2 hours
 *   maxSize: 200 * 1024 * 1024 // Override max size to 200MB
 * })
 * ```
 */
export function createCustomCacheConfig(overrides: Partial<CacheConfig>): CacheConfig {
  return {
    ...defaultCacheConfig,
    ...overrides,
    ttl: {
      ...defaultCacheConfig.ttl,
      ...(overrides.ttl || {})
    },
    memory: {
      ...defaultCacheConfig.memory,
      ...(overrides.memory || {})
    },
    invalidation: {
      ...defaultCacheConfig.invalidation,
      ...(overrides.invalidation || {})
    }
  }
}
