/**
 * Cache Service Module
 *
 * This module provides a unified interface for caching operations
 * across different cache providers. It exports all cache-related
 * functionality including service implementations, key builders,
 * factory functions, and configuration presets.
 *
 * The module follows the index.ts design principle by only containing
 * re-exports and no implementation code. All concrete implementations
 * are located in separate, focused modules.
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */

// Export cache service implementation and interfaces
export { CacheServiceImpl } from './cache-service'
export { CacheKeyBuilderImpl } from './cache-key-builder'

// Export factory functions and singleton instances
export {
  createCacheService,
  createMemoryCacheService,
  createValidatedCacheService,
  cacheService
} from './cache-factory'

// Export configuration presets and utilities
export {
  defaultCacheConfig,
  developmentCacheConfig,
  productionCacheConfig,
  testingCacheConfig,
  getCacheConfigByEnvironment,
  createCustomCacheConfig
} from './cache-config'

// Export cache providers
export { MemoryCacheProvider } from './memory-cache'

// Re-export types for convenience
export type {
  CacheService,
  CacheProvider,
  CacheConfig,
  CacheStats,
  CacheKeyBuilder,
  CacheMiddleware
} from '../../types/cache'
