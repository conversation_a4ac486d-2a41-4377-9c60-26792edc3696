/**
 * Cache Key Builder Implementation
 * 
 * This module provides a concrete implementation of the CacheKeyBuilder interface
 * for generating consistent, unique cache keys across different content types
 * and operations. The key builder ensures proper namespacing, collision avoidance,
 * and supports efficient cache invalidation patterns.
 * 
 * Key Features:
 * - Hierarchical key structure with configurable prefix
 * - Content-type aware key generation
 * - Locale-specific key namespacing
 * - Options hashing for query-based cache keys
 * - Tag generation for cache invalidation
 * - Key parsing utilities for debugging and analytics
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */

import type {
  CacheKeyBuilder
} from '../../types/cache'

import type {
  ContentType
} from '../../types'

/**
 * Cache Key Builder Implementation
 * 
 * Generates consistent cache keys for different types of content
 * and operations, ensuring uniqueness and enabling efficient
 * cache invalidation patterns.
 * 
 * The key structure follows the pattern:
 * `{prefix}:{operation}:{type}:{locale}:{identifier}`
 * 
 * Examples:
 * - Content: `cms:content:blog:en:my-post-slug`
 * - List: `cms:list:blog:en:abc123ef` (options hash)
 * - Metadata: `cms:metadata:blog:en:my-post-slug`
 * 
 * @implements {CacheKeyBuilder}
 */
export class CacheKeyBuilderImpl implements CacheKeyBuilder {
  private readonly prefix: string

  /**
   * Creates a new cache key builder instance
   * 
   * @param prefix - The prefix to use for all cache keys (default: 'cms')
   */
  constructor(prefix: string = 'cms') {
    this.prefix = prefix
  }

  /**
   * Generates a cache key for individual content items
   * 
   * Creates a unique key for a specific piece of content identified
   * by its type, slug, and locale. This key is used for caching
   * the full content object including metadata and body.
   * 
   * @param type - The content type (e.g., 'blog', 'product')
   * @param slug - The unique slug identifier for the content
   * @param locale - The language locale (e.g., 'en', 'zh')
   * @returns A unique cache key for the content item
   * 
   * @example
   * ```typescript
   * const key = builder.contentKey('blog', 'my-post', 'en')
   * // Returns: 'cms:content:blog:en:my-post'
   * ```
   */
  contentKey(type: ContentType, slug: string, locale: string): string {
    return `${this.prefix}:content:${type}:${locale}:${slug}`
  }

  /**
   * Generates a cache key for content lists
   * 
   * Creates a unique key for a list of content items based on
   * the content type, locale, and query options. The options
   * are hashed to create a deterministic key that can be
   * reproduced for the same query parameters.
   * 
   * @param type - The content type to list
   * @param locale - The language locale for the list
   * @param options - Optional query parameters (filters, sorting, pagination)
   * @returns A unique cache key for the content list
   * 
   * @example
   * ```typescript
   * const key = builder.contentListKey('blog', 'en', { featured: true })
   * // Returns: 'cms:list:blog:en:abc123ef'
   * ```
   */
  contentListKey(type: ContentType, locale: string, options?: any): string {
    const optionsHash = options ? this.hashOptions(options) : 'default'
    return `${this.prefix}:list:${type}:${locale}:${optionsHash}`
  }

  /**
   * Generates a cache key for content metadata
   * 
   * Creates a key specifically for caching content metadata
   * such as word count, reading time, and other computed
   * properties that don't change frequently.
   * 
   * @param type - The content type
   * @param slug - The content slug identifier
   * @param locale - The language locale
   * @returns A unique cache key for the content metadata
   * 
   * @example
   * ```typescript
   * const key = builder.metadataKey('blog', 'my-post', 'en')
   * // Returns: 'cms:metadata:blog:en:my-post'
   * ```
   */
  metadataKey(type: ContentType, slug: string, locale: string): string {
    return `${this.prefix}:metadata:${type}:${locale}:${slug}`
  }

  /**
   * Generates a cache key for SEO data
   * 
   * Creates a key for caching SEO-related information such as
   * meta tags, Open Graph data, and structured data that is
   * generated from content but cached separately for performance.
   * 
   * @param type - The content type
   * @param slug - The content slug identifier
   * @param locale - The language locale
   * @returns A unique cache key for the SEO data
   * 
   * @example
   * ```typescript
   * const key = builder.seoKey('blog', 'my-post', 'en')
   * // Returns: 'cms:seo:blog:en:my-post'
   * ```
   */
  seoKey(type: ContentType, slug: string, locale: string): string {
    return `${this.prefix}:seo:${type}:${locale}:${slug}`
  }

  /**
   * Generates a cache key for sitemap data
   * 
   * Creates a key for caching generated sitemap XML data.
   * Can be locale-specific or global depending on the
   * sitemap generation strategy.
   * 
   * @param locale - Optional language locale for locale-specific sitemaps
   * @returns A unique cache key for the sitemap
   * 
   * @example
   * ```typescript
   * const globalKey = builder.sitemapKey()
   * // Returns: 'cms:sitemap'
   * 
   * const localeKey = builder.sitemapKey('en')
   * // Returns: 'cms:sitemap:en'
   * ```
   */
  sitemapKey(locale?: string): string {
    return `${this.prefix}:sitemap${locale ? `:${locale}` : ''}`
  }

  /**
   * Generates a cache key for RSS feed data
   *
   * Creates a key for caching generated RSS feed XML data
   * for a specific locale. RSS feeds are typically
   * locale-specific due to content language differences.
   *
   * @param locale - The language locale for the RSS feed
   * @returns A unique cache key for the RSS feed
   *
   * @example
   * ```typescript
   * const key = builder.rssKey('en')
   * // Returns: 'cms:rss:en'
   * ```
   */
  rssKey(locale: string): string {
    return `${this.prefix}:rss:${locale}`
  }

  /**
   * Parses a cache key to extract its components
   *
   * Breaks down a cache key into its constituent parts for
   * debugging, analytics, or cache management purposes.
   * This is useful for understanding what type of data
   * is cached under a specific key.
   *
   * @param key - The cache key to parse
   * @returns An object containing the parsed key components
   *
   * @example
   * ```typescript
   * const parsed = builder.parseKey('cms:content:blog:en:my-post')
   * // Returns: { type: 'content', identifier: 'blog:en:my-post', locale: 'en' }
   * ```
   */
  parseKey(key: string): { type: string; identifier: string; locale?: string } {
    const parts = key.split(':')
    return {
      type: parts[1] || 'unknown',
      identifier: parts.slice(2).join(':'),
      locale: parts[3]
    }
  }

  /**
   * Generates tags for cache invalidation
   *
   * Creates a list of tags that can be used to group related
   * cache entries for bulk invalidation. Tags are derived
   * from the cache key structure and enable efficient
   * cache management strategies.
   *
   * @param key - The cache key to generate tags for
   * @returns An array of tags for cache invalidation
   *
   * @example
   * ```typescript
   * const tags = builder.generateTags('cms:content:blog:en:my-post')
   * // Returns: ['content', 'locale:en']
   * ```
   */
  generateTags(key: string): string[] {
    const parsed = this.parseKey(key)
    const tags = [parsed.type]

    if (parsed.locale) {
      tags.push(`locale:${parsed.locale}`)
    }

    return tags
  }

  /**
   * Hashes query options for deterministic cache keys
   *
   * Creates a short, deterministic hash from query options
   * to ensure that the same query parameters always produce
   * the same cache key. This enables efficient cache hits
   * for repeated queries with identical parameters.
   *
   * @private
   * @param options - The query options to hash
   * @returns A short hash string representing the options
   *
   * @example
   * ```typescript
   * const hash = this.hashOptions({ featured: true, limit: 10 })
   * // Returns: 'abc123ef'
   * ```
   */
  private hashOptions(options: any): string {
    try {
      // Sort keys to ensure consistent hashing regardless of property order
      const sorted = JSON.stringify(options, Object.keys(options).sort())
      // Create a short base64 hash for compact cache keys
      return Buffer.from(sorted).toString('base64').slice(0, 8)
    } catch {
      // Fallback for non-serializable options
      return 'invalid'
    }
  }
}
