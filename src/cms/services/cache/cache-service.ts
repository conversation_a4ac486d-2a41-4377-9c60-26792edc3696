/**
 * Cache Service Implementation
 * 
 * This module provides the main cache service implementation that coordinates
 * between different cache providers and provides content-specific caching
 * functionality with automatic key generation and invalidation.
 * 
 * The service acts as a high-level abstraction over various cache backends
 * (memory, Redis, file-based) and provides specialized methods for caching
 * different types of CMS content with appropriate TTL settings and
 * invalidation strategies.
 * 
 * Key Features:
 * - Multi-provider cache backend support
 * - Content-aware caching strategies
 * - Automatic key generation and namespacing
 * - Tag-based cache invalidation
 * - Middleware support for monitoring and custom processing
 * - Performance statistics and monitoring
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */

import type {
  CacheService,
  CacheProvider,
  CacheConfig,
  CacheStats,
  CacheKeyBuilder,
  CacheMiddleware
} from '../../types/cache'

import type {
  ContentItem,
  ContentType
} from '../../types'

import { CacheKeyBuilderImpl } from './cache-key-builder'
import { MemoryCacheProvider } from './memory-cache'

/**
 * Cache Service Implementation
 * 
 * Main cache service that coordinates between different cache providers
 * and provides content-specific caching functionality with automatic
 * key generation and invalidation.
 * 
 * The service supports multiple caching strategies and provides a unified
 * interface for caching various types of content including individual
 * items, lists, metadata, and SEO data.
 * 
 * @implements {CacheService}
 */
export class CacheServiceImpl implements CacheService {
  private readonly provider: CacheProvider
  private readonly keyBuilder: CacheKeyBuilder
  private readonly middleware: CacheMiddleware[] = []
  private readonly config: CacheConfig

  /**
   * Creates a new cache service instance
   * 
   * Initializes the cache service with the specified configuration,
   * sets up the appropriate cache provider based on the strategy,
   * and configures the key builder for consistent key generation.
   * 
   * @param config - Cache configuration including strategy, TTL settings, and provider options
   * @throws {Error} When an unsupported cache strategy is specified
   * 
   * @example
   * ```typescript
   * const config = {
   *   strategy: 'memory',
   *   ttl: { content: 3600, contentList: 1800 },
   *   memory: { maxSize: 100 * 1024 * 1024 }
   * }
   * const cacheService = new CacheServiceImpl(config)
   * ```
   */
  constructor(config: CacheConfig) {
    this.config = config
    this.keyBuilder = new CacheKeyBuilderImpl()
    
    // Initialize cache provider based on strategy
    switch (config.strategy) {
      case 'memory':
        this.provider = new MemoryCacheProvider(config.memory)
        break
      
      case 'redis':
        // TODO: Implement Redis provider
        throw new Error('Redis cache provider not yet implemented')
      
      case 'file':
        // TODO: Implement file cache provider
        throw new Error('File cache provider not yet implemented')
      
      case 'hybrid':
        // TODO: Implement hybrid cache provider
        throw new Error('Hybrid cache provider not yet implemented')
      
      default:
        throw new Error(`Unknown cache strategy: ${config.strategy}`)
    }
  }

  /**
   * Cache content item
   * 
   * Stores a content item in the cache with appropriate TTL
   * and tags for efficient retrieval and invalidation.
   * 
   * The method automatically generates cache keys, applies
   * middleware hooks, and sets up invalidation tags for
   * efficient cache management.
   * 
   * @param type - Content type (e.g., 'blog', 'product')
   * @param slug - Content slug identifier
   * @param locale - Language locale
   * @param content - Content item to cache
   * 
   * @example
   * ```typescript
   * await cacheService.cacheContent('blog', 'my-post', 'en', blogPost)
   * ```
   */
  async cacheContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string,
    content: T
  ): Promise<void> {
    const key = this.keyBuilder.contentKey(type, slug, locale)
    const ttl = this.config.ttl.content
    
    await this.executeWithMiddleware('set', key, async () => {
      await this.provider.set(key, content, ttl)
      
      // Add tags for invalidation
      const tags = [
        `content:${type}`,
        `locale:${locale}`,
        `content:${type}:${locale}`
      ]
      await this.provider.tagKey(key, tags)
    })
  }

  /**
   * Get cached content item
   * 
   * Retrieves a content item from the cache if available
   * and not expired. Returns null if the item is not
   * found or has expired.
   * 
   * @param type - Content type
   * @param slug - Content slug identifier
   * @param locale - Language locale
   * @returns Cached content item or null if not found
   * 
   * @example
   * ```typescript
   * const blogPost = await cacheService.getCachedContent<BlogContent>('blog', 'my-post', 'en')
   * if (blogPost) {
   *   // Use cached content
   * }
   * ```
   */
  async getCachedContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null> {
    const key = this.keyBuilder.contentKey(type, slug, locale)
    
    return this.executeWithMiddleware('get', key, async () => {
      return this.provider.get<T>(key)
    })
  }

  /**
   * Cache content list
   * 
   * Stores a list of content items with query options
   * for efficient list retrieval. The query options
   * are hashed to create a unique cache key.
   * 
   * @param type - Content type
   * @param locale - Language locale
   * @param content - Array of content items
   * @param options - Query options used to generate the list
   * 
   * @example
   * ```typescript
   * const options = { featured: true, limit: 10 }
   * await cacheService.cacheContentList('blog', 'en', blogPosts, options)
   * ```
   */
  async cacheContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    content: T[],
    options?: any
  ): Promise<void> {
    const key = this.keyBuilder.contentListKey(type, locale, options)
    const ttl = this.config.ttl.contentList
    
    await this.executeWithMiddleware('set', key, async () => {
      await this.provider.set(key, content, ttl)
      
      // Add tags for invalidation
      const tags = [
        `list:${type}`,
        `locale:${locale}`,
        `list:${type}:${locale}`
      ]
      await this.provider.tagKey(key, tags)
    })
  }

  /**
   * Get cached content list
   *
   * Retrieves a cached list of content items if available.
   * The query options must match exactly for a cache hit.
   *
   * @param type - Content type
   * @param locale - Language locale
   * @param options - Query options to match cached list
   * @returns Cached content list or null if not found
   *
   * @example
   * ```typescript
   * const options = { featured: true, limit: 10 }
   * const blogPosts = await cacheService.getCachedContentList<BlogContent>('blog', 'en', options)
   * ```
   */
  async getCachedContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options?: any
  ): Promise<T[] | null> {
    const key = this.keyBuilder.contentListKey(type, locale, options)

    return this.executeWithMiddleware('get', key, async () => {
      return this.provider.get<T[]>(key)
    })
  }

  /**
   * Cache SEO data
   *
   * Stores SEO-related data with appropriate TTL.
   * SEO data includes meta tags, Open Graph information,
   * and structured data that is generated from content.
   *
   * @param key - Cache key for SEO data
   * @param data - SEO data to cache
   *
   * @example
   * ```typescript
   * const seoKey = keyBuilder.seoKey('blog', 'my-post', 'en')
   * await cacheService.cacheSEOData(seoKey, seoData)
   * ```
   */
  async cacheSEOData(key: string, data: any): Promise<void> {
    const ttl = this.config.ttl.seo

    await this.executeWithMiddleware('set', key, async () => {
      await this.provider.set(key, data, ttl)

      // Add SEO tag for bulk invalidation
      await this.provider.tagKey(key, ['seo'])
    })
  }

  /**
   * Get cached SEO data
   *
   * Retrieves cached SEO data if available.
   * Returns null if the data is not found or has expired.
   *
   * @param key - Cache key for SEO data
   * @returns Cached SEO data or null if not found
   *
   * @example
   * ```typescript
   * const seoKey = keyBuilder.seoKey('blog', 'my-post', 'en')
   * const seoData = await cacheService.getCachedSEOData<SEOData>(seoKey)
   * ```
   */
  async getCachedSEOData<T>(key: string): Promise<T | null> {
    return this.executeWithMiddleware('get', key, async () => {
      return this.provider.get<T>(key)
    })
  }

  /**
   * Invalidate content cache
   *
   * Removes cached content based on type, slug, and locale.
   * Supports partial invalidation for efficient cache management.
   *
   * This method provides flexible invalidation strategies:
   * - Specific content item (when type, slug, and locale are provided)
   * - All content of a type for a specific locale
   * - All content of a type across all locales
   *
   * @param type - Content type to invalidate
   * @param slug - Optional specific slug to invalidate
   * @param locale - Optional specific locale to invalidate
   *
   * @example
   * ```typescript
   * // Invalidate specific content item
   * await cacheService.invalidateContent('blog', 'my-post', 'en')
   *
   * // Invalidate all blog content for English locale
   * await cacheService.invalidateContent('blog', undefined, 'en')
   *
   * // Invalidate all blog content across all locales
   * await cacheService.invalidateContent('blog')
   * ```
   */
  async invalidateContent(
    type: ContentType,
    slug?: string,
    locale?: string
  ): Promise<void> {
    if (slug && locale) {
      // Invalidate specific content item and related data
      const contentKey = this.keyBuilder.contentKey(type, slug, locale)
      const metadataKey = this.keyBuilder.metadataKey(type, slug, locale)
      const seoKey = this.keyBuilder.seoKey(type, slug, locale)

      await this.provider.deleteMany([contentKey, metadataKey, seoKey])
    } else if (locale) {
      // Invalidate all content of type for specific locale
      await this.provider.deleteByTag(`content:${type}:${locale}`)
      await this.provider.deleteByTag(`list:${type}:${locale}`)
    } else {
      // Invalidate all content of type across all locales
      await this.provider.deleteByTag(`content:${type}`)
      await this.provider.deleteByTag(`list:${type}`)
    }
  }

  /**
   * Invalidate all cache
   *
   * Clears all cached data. Use with caution in production
   * as this will force all subsequent requests to regenerate
   * cached content, potentially causing performance issues.
   *
   * @example
   * ```typescript
   * // Clear all cache (use sparingly)
   * await cacheService.invalidateAll()
   * ```
   */
  async invalidateAll(): Promise<void> {
    await this.provider.clear()
  }

  /**
   * Get cache statistics
   *
   * Returns comprehensive statistics about cache performance
   * including hit rates, memory usage, and operation counts.
   * This information is useful for monitoring and optimization.
   *
   * @returns Cache statistics object
   *
   * @example
   * ```typescript
   * const stats = await cacheService.getStats()
   * console.log(`Cache hit rate: ${stats.hitRate}%`)
   * console.log(`Memory usage: ${stats.memoryUsage} bytes`)
   * ```
   */
  async getStats(): Promise<CacheStats> {
    return this.provider.stats()
  }

  /**
   * Add middleware
   *
   * Registers middleware for cache operations monitoring
   * and custom processing. Middleware can be used for
   * logging, metrics collection, or custom cache behaviors.
   *
   * @param middleware - Middleware to add
   *
   * @example
   * ```typescript
   * const loggingMiddleware = {
   *   beforeGet: async (key) => console.log(`Getting ${key}`),
   *   afterGet: async (key, value, hit) => console.log(`Got ${key}: ${hit ? 'HIT' : 'MISS'}`)
   * }
   * cacheService.use(loggingMiddleware)
   * ```
   */
  use(middleware: CacheMiddleware): void {
    this.middleware.push(middleware)
  }

  /**
   * Connect to cache provider
   *
   * Initializes connection to the cache backend.
   * This method should be called before using the cache service,
   * especially for providers that require network connections.
   *
   * @example
   * ```typescript
   * await cacheService.connect()
   * ```
   */
  async connect(): Promise<void> {
    if (this.provider.connect) {
      await this.provider.connect()
    }
  }

  /**
   * Disconnect from cache provider
   *
   * Closes connection to the cache backend and performs
   * any necessary cleanup. This should be called when
   * shutting down the application.
   *
   * @example
   * ```typescript
   * await cacheService.disconnect()
   * ```
   */
  async disconnect(): Promise<void> {
    if (this.provider.disconnect) {
      await this.provider.disconnect()
    }
  }

  // Private helper methods

  /**
   * Execute cache operation with middleware hooks
   *
   * Wraps cache operations with middleware execution for
   * monitoring, logging, and custom processing. This method
   * ensures that all middleware hooks are called in the
   * correct order and handles errors appropriately.
   *
   * @private
   * @param operation - The type of cache operation ('get' or 'set')
   * @param key - The cache key being operated on
   * @param fn - The actual cache operation function
   * @returns The result of the cache operation
   */
  private async executeWithMiddleware<T>(
    operation: string,
    key: string,
    fn: () => Promise<T>
  ): Promise<T> {
    // Execute before hooks
    for (const middleware of this.middleware) {
      if (operation === 'get' && middleware.beforeGet) {
        await middleware.beforeGet(key)
      } else if (operation === 'set' && middleware.beforeSet) {
        await middleware.beforeSet(key, undefined)
      }
    }

    try {
      const result = await fn()

      // Execute after hooks
      for (const middleware of this.middleware) {
        if (operation === 'get' && middleware.afterGet) {
          await middleware.afterGet(key, result, result !== null)
        } else if (operation === 'set' && middleware.afterSet) {
          await middleware.afterSet(key, result)
        }
      }

      return result
    } catch (error) {
      // Execute error hooks
      for (const middleware of this.middleware) {
        if (middleware.onError) {
          await middleware.onError(error as Error, operation, key)
        }
      }
      throw error
    }
  }
}
