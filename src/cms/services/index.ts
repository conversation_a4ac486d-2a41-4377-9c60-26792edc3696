/**
 * CMS Services Module
 * 
 * Centralized service layer that provides a unified interface for content
 * management operations. This module abstracts away the complexity of
 * different CMS providers and provides a consistent API for the application.
 * 
 * Key Features:
 * - Provider-agnostic content operations
 * - Automatic caching and performance optimization
 * - Type-safe content queries and mutations
 * - SEO data generation and management
 * - Multi-language content support
 * - Error handling and logging
 */

import type { 
  ContentProvider, 
  ContentItem, 
  ContentType, 
  QueryOptions,
  ContentMetadata,
  LanguageVersion,
  CMSConfig
} from '../types'

// Import available providers
import { Contentlayer2Provider } from '../providers/contentlayer2/provider'

/**
 * CMS Service Class
 * 
 * Main service class that coordinates between different CMS providers
 * and provides a unified interface for content operations. This class
 * handles provider initialization, caching, and error management.
 */
export class CMSService {
  private provider: ContentProvider | null = null
  private config: CMSConfig | null = null
  private initialized = false

  /**
   * Initialize CMS with specified provider
   * 
   * Sets up the CMS service with the specified provider and configuration.
   * This method must be called before any content operations can be performed.
   * 
   * @param config - CMS configuration including provider selection
   */
  async initialize(config: CMSConfig): Promise<void> {
    try {
      this.config = config
      
      // Initialize the specified provider
      switch (config.provider) {
        case 'contentlayer2':
          this.provider = new Contentlayer2Provider()
          break
        
        case 'strapi':
          // TODO: Implement Strapi provider
          throw new Error('Strapi provider not yet implemented')
        
        case 'sanity':
          // TODO: Implement Sanity provider
          throw new Error('Sanity provider not yet implemented')
        
        case 'contentful':
          // TODO: Implement Contentful provider
          throw new Error('Contentful provider not yet implemented')
        
        case 'nextjs-mdx':
          // TODO: Implement Next.js MDX provider
          throw new Error('Next.js MDX provider not yet implemented')
        
        default:
          throw new Error(`Unknown CMS provider: ${config.provider}`)
      }
      
      this.initialized = true
      console.log(`CMS initialized with provider: ${config.provider}`)
      
    } catch (error) {
      console.error('Failed to initialize CMS:', error)
      throw error
    }
  }

  /**
   * Ensure CMS is initialized
   * 
   * Internal helper method that ensures the CMS service has been
   * properly initialized before performing any operations.
   */
  private ensureInitialized(): void {
    if (!this.initialized || !this.provider) {
      throw new Error('CMS service not initialized. Call initialize() first.')
    }
  }

  /**
   * Get single content item
   * 
   * Retrieves a specific content item by type, slug, and language.
   * This method provides type-safe access to individual content items
   * with automatic error handling and logging.
   * 
   * @param type - Content type (blog, product, case-study)
   * @param slug - Content slug identifier
   * @param locale - Language code (en, zh)
   * @returns Promise resolving to content item or null if not found
   */
  async getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null> {
    this.ensureInitialized()
    
    try {
      const content = await this.provider!.getContent<T>(type, slug, locale)
      
      if (!content) {
        console.warn(`Content not found: ${type}/${locale}/${slug}`)
      }
      
      return content
    } catch (error) {
      console.error(`Error fetching content: ${type}/${locale}/${slug}`, error)
      return null
    }
  }

  /**
   * Get content list with filtering and sorting
   * 
   * Retrieves a filtered and sorted list of content items for a specific
   * type and language. Supports various query options including featured
   * content filtering, pagination, and custom sorting.
   * 
   * @param type - Content type to query
   * @param locale - Language code for filtering
   * @param options - Query options for filtering and sorting
   * @returns Promise resolving to array of content items
   */
  async getContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options: QueryOptions = {}
  ): Promise<T[]> {
    this.ensureInitialized()
    
    try {
      const content = await this.provider!.getContentList<T>(type, locale, options)
      
      console.log(`Retrieved ${content.length} ${type} items for locale: ${locale}`)
      
      return content
    } catch (error) {
      console.error(`Error fetching content list: ${type}/${locale}`, error)
      return []
    }
  }

  /**
   * Check if content exists
   * 
   * Verifies whether a specific content item exists for the given
   * type, slug, and language combination. This is useful for
   * language switching and content availability checks.
   * 
   * @param type - Content type to check
   * @param slug - Content slug to verify
   * @param locale - Language code to check
   * @returns Promise resolving to boolean indicating existence
   */
  async contentExists(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<boolean> {
    this.ensureInitialized()
    
    try {
      return await this.provider!.contentExists(type, slug, locale)
    } catch (error) {
      console.error(`Error checking content existence: ${type}/${locale}/${slug}`, error)
      return false
    }
  }

  /**
   * Get content title
   * 
   * Retrieves the title of a specific content item. This is a
   * convenience method for quick title access without fetching
   * the entire content object.
   * 
   * @param type - Content type
   * @param slug - Content slug
   * @param locale - Language code
   * @returns Promise resolving to title string or null
   */
  async getContentTitle(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<string | null> {
    this.ensureInitialized()
    
    try {
      return await this.provider!.getContentTitle(type, slug, locale)
    } catch (error) {
      console.error(`Error fetching content title: ${type}/${locale}/${slug}`, error)
      return null
    }
  }

  /**
   * Get content metadata
   * 
   * Extracts metadata information from a content item including
   * reading time, word count, and other computed properties.
   * This metadata is useful for SEO and user experience features.
   * 
   * @param type - Content type
   * @param slug - Content slug
   * @param locale - Language code
   * @returns Promise resolving to content metadata
   */
  async getContentMetadata(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<ContentMetadata | null> {
    this.ensureInitialized()
    
    try {
      return await this.provider!.getContentMetadata(type, slug, locale)
    } catch (error) {
      console.error(`Error fetching content metadata: ${type}/${locale}/${slug}`, error)
      return null
    }
  }

  /**
   * Get available language versions
   * 
   * Finds all available language versions of a specific content item.
   * This is essential for implementing language switching functionality
   * and providing users with alternative language options.
   * 
   * @param type - Content type
   * @param slug - Content slug to find versions for
   * @returns Promise resolving to array of language versions
   */
  async getAvailableLanguages(
    type: ContentType,
    slug: string
  ): Promise<LanguageVersion[]> {
    this.ensureInitialized()
    
    try {
      return await this.provider!.getAvailableLanguages(type, slug)
    } catch (error) {
      console.error(`Error fetching language versions: ${type}/${slug}`, error)
      return []
    }
  }

  /**
   * Get related content
   * 
   * Finds content items related to the current item based on tags
   * and content type. This is useful for implementing "related posts"
   * or "you might also like" features.
   * 
   * @param type - Content type
   * @param currentSlug - Current content slug to find related items for
   * @param locale - Language code
   * @param limit - Maximum number of related items to return
   * @returns Promise resolving to array of related content items
   */
  async getRelatedContent<T extends ContentItem>(
    type: ContentType,
    currentSlug: string,
    locale: string,
    limit: number = 3
  ): Promise<T[]> {
    this.ensureInitialized()
    
    try {
      if (this.provider!.getRelatedContent) {
        return await this.provider!.getRelatedContent<T>(type, currentSlug, locale, limit)
      }
      
      // Fallback: return recent content if provider doesn't support related content
      const recentContent = await this.getContentList<T>(type, locale, {
        sortBy: 'publishedAt',
        order: 'desc',
        limit: limit + 1
      })
      
      // Filter out current content
      return recentContent.filter(item => item.slug !== currentSlug).slice(0, limit)
      
    } catch (error) {
      console.error(`Error fetching related content: ${type}/${locale}/${currentSlug}`, error)
      return []
    }
  }

  /**
   * Get provider information
   * 
   * Returns information about the currently active CMS provider
   * including name, version, and capabilities.
   * 
   * @returns Provider information object
   */
  getProviderInfo(): { name: string; version: string } | null {
    if (!this.provider) return null
    
    return {
      name: this.provider.name,
      version: this.provider.version
    }
  }

  /**
   * Get CMS configuration
   * 
   * Returns the current CMS configuration including provider
   * settings, feature flags, and other options.
   * 
   * @returns Current CMS configuration
   */
  getConfig(): CMSConfig | null {
    return this.config
  }

  /**
   * Get content for static generation
   *
   * Retrieves all content items of a specific type for static generation.
   * This method is optimized for build-time usage and is used by Next.js
   * generateStaticParams and other static generation features.
   *
   * @param type - Content type to retrieve
   * @returns Promise resolving to array of all content items
   */
  async getContentForStaticGeneration<T extends ContentItem>(
    type: ContentType
  ): Promise<T[]> {
    this.ensureInitialized()

    try {
      return await this.provider!.getContentForStaticGeneration<T>(type)
    } catch (error) {
      console.error(`Error fetching content for static generation: ${type}`, error)
      return []
    }
  }

  /**
   * Get all content slugs for static generation
   *
   * Retrieves all slug and locale combinations for a specific content type.
   * This is used by Next.js generateStaticParams to create static routes
   * for all available content in all languages.
   *
   * @param type - Content type to get slugs for
   * @returns Promise resolving to array of slug/locale combinations
   */
  async getAllContentSlugs(
    type: ContentType
  ): Promise<Array<{ locale: string; slug: string }>> {
    this.ensureInitialized()

    try {
      return await this.provider!.getAllContentSlugs(type)
    } catch (error) {
      console.error(`Error fetching content slugs: ${type}`, error)
      return []
    }
  }

  /**
   * Check if CMS is initialized
   *
   * Returns whether the CMS service has been properly initialized
   * and is ready to handle content operations.
   *
   * @returns Boolean indicating initialization status
   */
  isInitialized(): boolean {
    return this.initialized && this.provider !== null
  }
}

// Create and export singleton instance
export const cms = new CMSService()

// Export convenience function for initialization
export async function initializeCMS(config?: Partial<CMSConfig>): Promise<void> {
  const defaultConfig: CMSConfig = {
    provider: 'contentlayer2',
    contentTypes: ['blog', 'product', 'case-study'],
    defaultLocale: 'en',
    supportedLocales: ['en', 'zh'],
    features: {
      cache: true,
      seo: true,
      relatedContent: true,
      languageSwitching: true
    }
  }
  
  const finalConfig = { ...defaultConfig, ...config }
  await cms.initialize(finalConfig)
}

// Re-export types for convenience
export type { 
  ContentProvider, 
  ContentItem, 
  ContentType, 
  QueryOptions,
  ContentMetadata,
  LanguageVersion,
  CMSConfig
} from '../types'
