/**
 * CMS Configuration Types
 * 
 * This module defines configuration interfaces and types for the CMS system.
 * The configuration system provides flexible setup options for different
 * CMS providers, feature flags, performance settings, and deployment
 * environments.
 * 
 * The configuration types support multiple CMS backends and provide
 * fine-grained control over system behavior, caching strategies,
 * and feature enablement.
 * 
 * Key Features:
 * - Multi-provider CMS configuration
 * - Feature flag system for optional capabilities
 * - Performance and caching configuration
 * - Multilingual content support settings
 * - Environment-specific configuration options
 * - Type-safe configuration validation
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */

import type { ContentType } from './content'

/**
 * Supported CMS Provider Types
 * 
 * Defines the available CMS providers that can be configured
 * in the system. Each provider has its own specific configuration
 * requirements and capabilities.
 * 
 * @example
 * ```typescript
 * const provider: CMSProviderType = 'contentlayer2'
 * ```
 */
export type CMSProviderType = 
  | 'contentlayer2'  // File-based CMS with TypeScript support
  | 'strapi'         // Headless CMS with REST/GraphQL API
  | 'sanity'         // Real-time headless CMS
  | 'contentful'     // Cloud-based headless CMS
  | 'nextjs-mdx'     // Next.js built-in MDX support

/**
 * Cache Strategy Types
 * 
 * Defines the available caching strategies for content delivery.
 * Different strategies offer different trade-offs between
 * performance, memory usage, and scalability.
 * 
 * @example
 * ```typescript
 * const strategy: CacheStrategyType = 'memory'
 * ```
 */
export type CacheStrategyType = 
  | 'memory'   // In-memory caching (fast, limited by RAM)
  | 'redis'    // Redis-based caching (scalable, network overhead)
  | 'file'     // File-system caching (persistent, slower)

/**
 * CMS Feature Configuration
 * 
 * Defines which optional features are enabled in the CMS system.
 * Feature flags allow for gradual rollout of new capabilities
 * and environment-specific feature sets.
 * 
 * @example
 * ```typescript
 * const features: CMSFeatures = {
 *   cache: true,
 *   seo: true,
 *   relatedContent: false,
 *   languageSwitching: true
 * }
 * ```
 */
export interface CMSFeatures {
  /** Enable content caching for improved performance */
  cache: boolean
  /** Enable SEO data generation and optimization */
  seo: boolean
  /** Enable related content suggestions */
  relatedContent: boolean
  /** Enable language switching functionality */
  languageSwitching: boolean
}

/**
 * Cache Configuration
 * 
 * Defines caching behavior and performance settings.
 * This configuration is used when the cache feature
 * is enabled to optimize content delivery.
 * 
 * @example
 * ```typescript
 * const cacheConfig: CMSCacheConfig = {
 *   enabled: true,
 *   ttl: 3600, // 1 hour
 *   strategy: 'memory'
 * }
 * ```
 */
export interface CMSCacheConfig {
  /** Whether caching is enabled */
  enabled: boolean
  /** Time-to-live for cached content in seconds */
  ttl: number
  /** Caching strategy to use */
  strategy: CacheStrategyType
}

/**
 * SEO Configuration
 * 
 * Defines SEO-related settings and optimization options.
 * This configuration controls how SEO data is generated
 * and what optimization features are enabled.
 * 
 * @example
 * ```typescript
 * const seoConfig: CMSSEOConfig = {
 *   generateSitemap: true,
 *   generateRobotsTxt: true,
 *   defaultMetaDescription: 'Default site description',
 *   openGraphDefaults: {
 *     siteName: 'My Website',
 *     type: 'website'
 *   }
 * }
 * ```
 */
export interface CMSSEOConfig {
  /** Generate XML sitemap automatically */
  generateSitemap: boolean
  /** Generate robots.txt file */
  generateRobotsTxt: boolean
  /** Default meta description for pages without one */
  defaultMetaDescription?: string
  /** Default Open Graph properties */
  openGraphDefaults?: {
    siteName?: string
    type?: 'website' | 'article'
    image?: string
  }
}

/**
 * Multilingual Configuration
 * 
 * Defines settings for multilingual content support
 * including default locale, supported languages, and
 * URL structure preferences.
 * 
 * @example
 * ```typescript
 * const i18nConfig: CMSI18nConfig = {
 *   defaultLocale: 'en',
 *   supportedLocales: ['en', 'zh', 'es'],
 *   urlStrategy: 'prefix',
 *   fallbackToDefault: true
 * }
 * ```
 */
export interface CMSI18nConfig {
  /** Default language locale */
  defaultLocale: string
  /** Array of supported language locales */
  supportedLocales: string[]
  /** URL structure strategy for multilingual content */
  urlStrategy: 'prefix' | 'domain' | 'subdomain'
  /** Whether to fallback to default locale for missing translations */
  fallbackToDefault: boolean
}

/**
 * Performance Configuration
 * 
 * Defines performance-related settings including
 * optimization options, resource limits, and
 * build-time configurations.
 * 
 * @example
 * ```typescript
 * const perfConfig: CMSPerformanceConfig = {
 *   preloadCriticalContent: true,
 *   lazyLoadImages: true,
 *   optimizeImages: true,
 *   maxConcurrentRequests: 10
 * }
 * ```
 */
export interface CMSPerformanceConfig {
  /** Preload critical content for faster initial page loads */
  preloadCriticalContent: boolean
  /** Enable lazy loading for images */
  lazyLoadImages: boolean
  /** Optimize images automatically */
  optimizeImages: boolean
  /** Maximum number of concurrent content requests */
  maxConcurrentRequests: number
}

/**
 * Development Configuration
 * 
 * Defines development-specific settings including
 * debugging options, hot reload behavior, and
 * development server configurations.
 * 
 * @example
 * ```typescript
 * const devConfig: CMSDevConfig = {
 *   enableDebugLogs: true,
 *   hotReload: true,
 *   mockData: false,
 *   validateContent: true
 * }
 * ```
 */
export interface CMSDevConfig {
  /** Enable detailed debug logging */
  enableDebugLogs: boolean
  /** Enable hot reload for content changes */
  hotReload: boolean
  /** Use mock data instead of real CMS content */
  mockData: boolean
  /** Validate content structure and types */
  validateContent: boolean
}

/**
 * CMS Configuration Interface
 * 
 * Defines the complete configuration options for the CMS system
 * including provider selection, content settings, feature flags,
 * and performance optimizations.
 * 
 * This is the main configuration interface that applications
 * use to set up the CMS system according to their requirements.
 * 
 * @example
 * ```typescript
 * const cmsConfig: CMSConfig = {
 *   provider: 'contentlayer2',
 *   contentTypes: ['blog', 'product', 'case-study'],
 *   defaultLocale: 'en',
 *   supportedLocales: ['en', 'zh'],
 *   features: {
 *     cache: true,
 *     seo: true,
 *     relatedContent: false,
 *     languageSwitching: true
 *   },
 *   cache: {
 *     enabled: true,
 *     ttl: 3600,
 *     strategy: 'memory'
 *   }
 * }
 * ```
 */
export interface CMSConfig {
  // Provider configuration
  /** CMS provider to use */
  provider: CMSProviderType
  
  // Content configuration
  /** Array of content types to support */
  contentTypes: ContentType[]
  /** Default language locale */
  defaultLocale: string
  /** Array of supported language locales */
  supportedLocales: string[]
  
  // Feature flags
  /** Feature enablement configuration */
  features: CMSFeatures
  
  // Optional advanced configurations
  /** Caching configuration (when cache feature is enabled) */
  cache?: CMSCacheConfig
  /** SEO configuration (when SEO feature is enabled) */
  seo?: CMSSEOConfig
  /** Internationalization configuration */
  i18n?: CMSI18nConfig
  /** Performance optimization configuration */
  performance?: CMSPerformanceConfig
  /** Development-specific configuration */
  development?: CMSDevConfig
}

/**
 * Environment-Specific Configuration Presets
 * 
 * Predefined configuration presets for different deployment
 * environments. These presets provide sensible defaults
 * for common deployment scenarios.
 */

/**
 * Development Environment Configuration
 *
 * Optimized for development with debugging enabled,
 * shorter cache times, and development-friendly features.
 */
export const developmentConfig: Partial<CMSConfig> = {
  features: {
    cache: true,
    seo: false,
    relatedContent: false,
    languageSwitching: true
  },
  cache: {
    enabled: true,
    ttl: 300, // 5 minutes
    strategy: 'memory'
  } as CMSCacheConfig,
  development: {
    enableDebugLogs: true,
    hotReload: true,
    mockData: false,
    validateContent: true
  } as CMSDevConfig
}

/**
 * Production Environment Configuration
 *
 * Optimized for production with performance features
 * enabled, longer cache times, and production-ready settings.
 */
export const productionConfig: Partial<CMSConfig> = {
  features: {
    cache: true,
    seo: true,
    relatedContent: true,
    languageSwitching: true
  },
  cache: {
    enabled: true,
    ttl: 3600, // 1 hour
    strategy: 'memory'
  } as CMSCacheConfig,
  seo: {
    generateSitemap: true,
    generateRobotsTxt: true
  } as CMSSEOConfig,
  performance: {
    preloadCriticalContent: true,
    lazyLoadImages: true,
    optimizeImages: true,
    maxConcurrentRequests: 20
  } as CMSPerformanceConfig,
  development: {
    enableDebugLogs: false,
    hotReload: false,
    mockData: false,
    validateContent: false
  } as CMSDevConfig
}

/**
 * Testing Environment Configuration
 *
 * Optimized for testing with mock data support,
 * minimal caching, and testing-friendly features.
 */
export const testingConfig: Partial<CMSConfig> = {
  features: {
    cache: false,
    seo: false,
    relatedContent: false,
    languageSwitching: false
  },
  development: {
    enableDebugLogs: false,
    hotReload: false,
    mockData: true,
    validateContent: true
  } as CMSDevConfig
}

/**
 * Configuration Utility Functions
 */

/**
 * Merge configuration with environment preset
 * 
 * Utility function to merge a base configuration with
 * environment-specific presets for easy configuration
 * management across different deployment environments.
 * 
 * @param baseConfig - Base CMS configuration
 * @param environment - Target environment
 * @returns Merged configuration
 * 
 * @example
 * ```typescript
 * const config = mergeConfigWithEnvironment(baseConfig, 'production')
 * ```
 */
export function mergeConfigWithEnvironment(
  baseConfig: CMSConfig,
  environment: 'development' | 'production' | 'testing'
): CMSConfig {
  const envConfig = environment === 'development' ? developmentConfig
    : environment === 'production' ? productionConfig
    : testingConfig

  return {
    ...baseConfig,
    ...envConfig,
    features: {
      ...baseConfig.features,
      ...(envConfig.features || {})
    },
    cache: baseConfig.cache || envConfig.cache ? {
      ...(baseConfig.cache || {}),
      ...(envConfig.cache || {})
    } as CMSCacheConfig : undefined,
    seo: baseConfig.seo || envConfig.seo ? {
      ...(baseConfig.seo || {}),
      ...(envConfig.seo || {})
    } as CMSSEOConfig : undefined,
    performance: baseConfig.performance || envConfig.performance ? {
      ...(baseConfig.performance || {}),
      ...(envConfig.performance || {})
    } as CMSPerformanceConfig : undefined,
    development: baseConfig.development || envConfig.development ? {
      ...(baseConfig.development || {}),
      ...(envConfig.development || {})
    } as CMSDevConfig : undefined
  }
}
