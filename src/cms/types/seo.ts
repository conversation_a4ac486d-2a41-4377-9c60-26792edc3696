/**
 * SEO Types Module
 * 
 * Comprehensive type definitions for SEO functionality including
 * structured data, sitemap generation, RSS feeds, and meta tags.
 * These types ensure type safety across all SEO-related operations
 * and provide a consistent interface for different CMS providers.
 */

import type { ContentItem, ContentType } from './index'

/**
 * Structured Data Types
 * 
 * Defines the structure for JSON-LD structured data that helps
 * search engines understand the content and context of web pages.
 * Based on Schema.org vocabulary for maximum compatibility.
 */

export interface BaseStructuredData {
  '@context': 'https://schema.org'
  '@type': string
  name?: string
  description?: string
  url?: string
  image?: string | ImageObject
  datePublished?: string
  dateModified?: string
}

export interface ImageObject {
  '@type': 'ImageObject'
  url: string
  width?: number
  height?: number
  caption?: string
}

export interface PersonObject {
  '@type': 'Person'
  name: string
  image?: ImageObject | string
  url?: string
  sameAs?: string[]
}

export interface OrganizationObject {
  '@type': 'Organization'
  name: string
  url: string
  logo?: ImageObject
  description?: string
  sameAs?: string[]
}

export interface BlogPostingStructuredData extends BaseStructuredData {
  '@type': 'BlogPosting'
  headline: string
  author: PersonObject | OrganizationObject
  publisher: OrganizationObject
  mainEntityOfPage: {
    '@type': 'WebPage'
    '@id': string
  }
  keywords?: string
  wordCount?: number
  timeRequired?: string
  articleSection?: string
}

export interface ProductStructuredData extends BaseStructuredData {
  '@type': 'SoftwareApplication'
  applicationCategory: string
  operatingSystem: string
  offers?: {
    '@type': 'Offer'
    price: string
    priceCurrency: string
    availability?: string
  }
  aggregateRating?: {
    '@type': 'AggregateRating'
    ratingValue: number
    reviewCount: number
  }
}

export interface ArticleStructuredData extends BaseStructuredData {
  '@type': 'Article'
  headline: string
  author: PersonObject | OrganizationObject
  publisher: OrganizationObject
  mainEntityOfPage: {
    '@type': 'WebPage'
    '@id': string
  }
}

export type StructuredData = 
  | BlogPostingStructuredData 
  | ProductStructuredData 
  | ArticleStructuredData

/**
 * Sitemap Types
 * 
 * Defines the structure for XML sitemap generation including
 * URL entries, change frequencies, priorities, and alternate
 * language versions for international SEO.
 */

export type ChangeFrequency = 
  | 'always' 
  | 'hourly' 
  | 'daily' 
  | 'weekly' 
  | 'monthly' 
  | 'yearly' 
  | 'never'

export interface AlternateLanguage {
  hreflang: string
  href: string
}

export interface SitemapEntry {
  url: string
  lastmod?: string
  changefreq: ChangeFrequency
  priority: number
  alternates?: AlternateLanguage[]
}

export interface SitemapConfig {
  baseUrl: string
  defaultChangefreq: ChangeFrequency
  defaultPriority: number
  includeAlternates: boolean
  excludePatterns?: string[]
  staticPages: Array<{
    path: string
    priority?: number
    changefreq?: ChangeFrequency
  }>
}

/**
 * RSS Feed Types
 * 
 * Defines the structure for RSS feed generation including
 * channel information, item details, and multi-language
 * support for content syndication.
 */

export interface RSSItem {
  title: string
  description: string
  link: string
  guid: string
  pubDate: string
  author?: string
  category?: string[]
  enclosure?: {
    url: string
    type: string
    length: number
  }
}

export interface RSSChannel {
  title: string
  description: string
  link: string
  language: string
  lastBuildDate: string
  generator: string
  items: RSSItem[]
}

export interface RSSConfig {
  title: string
  description: string
  baseUrl: string
  language: string
  maxItems: number
  includeContent: boolean
  categories?: string[]
}

/**
 * Meta Tags Types
 * 
 * Defines the structure for HTML meta tags including
 * Open Graph, Twitter Cards, and other social media
 * optimization tags for better content sharing.
 */

export interface BasicMetaTags {
  title: string
  description: string
  keywords?: string
  canonical?: string
  robots?: string
  viewport?: string
}

export interface OpenGraphTags {
  'og:title': string
  'og:description': string
  'og:type': 'website' | 'article' | 'product'
  'og:url': string
  'og:image'?: string
  'og:image:width'?: number
  'og:image:height'?: number
  'og:image:alt'?: string
  'og:site_name'?: string
  'og:locale'?: string
  'og:locale:alternate'?: string[]
  'og:article:author'?: string
  'og:article:published_time'?: string
  'og:article:modified_time'?: string
  'og:article:section'?: string
  'og:article:tag'?: string[]
}

export interface TwitterCardTags {
  'twitter:card': 'summary' | 'summary_large_image' | 'app' | 'player'
  'twitter:site'?: string
  'twitter:creator'?: string
  'twitter:title': string
  'twitter:description': string
  'twitter:image'?: string
  'twitter:image:alt'?: string
}

export interface MetaTags extends BasicMetaTags {
  openGraph: OpenGraphTags
  twitter: TwitterCardTags
  structuredData?: StructuredData
}

/**
 * SEO Generator Configuration
 * 
 * Defines configuration options for SEO data generation
 * including which features to enable and how to customize
 * the output for different content types and languages.
 */

export interface SEOGeneratorConfig {
  baseUrl: string
  siteName: string
  defaultAuthor: string
  defaultImage: string
  
  // Feature flags
  features: {
    structuredData: boolean
    openGraph: boolean
    twitterCards: boolean
    sitemap: boolean
    rss: boolean
  }
  
  // Content type specific settings
  contentTypes: {
    [K in ContentType]: {
      structuredDataType: string
      defaultImage?: string
      includeInSitemap: boolean
      includeInRSS: boolean
      priority: number
      changefreq: ChangeFrequency
    }
  }
  
  // Language settings
  languages: {
    [locale: string]: {
      name: string
      hreflang: string
      rss: {
        title: string
        description: string
      }
    }
  }
}

/**
 * SEO Service Interface
 * 
 * Defines the contract for SEO service implementations
 * that can generate various SEO-related data and files
 * from content items and site configuration.
 */

export interface SEOService {
  // Meta tag generation
  generateMetaTags(content: ContentItem, locale: string): Promise<MetaTags>
  
  // Structured data generation
  generateStructuredData(content: ContentItem): Promise<StructuredData>
  
  // Sitemap generation
  generateSitemap(content: ContentItem[], config: SitemapConfig): Promise<string>
  
  // RSS feed generation
  generateRSSFeed(
    content: ContentItem[], 
    config: RSSConfig, 
    locale: string
  ): Promise<string>
  
  // Bulk operations
  generateAllSEOData(locale?: string): Promise<{
    sitemap: string
    rssFeeds: { [locale: string]: string }
    structuredData: { [contentId: string]: StructuredData }
  }>
}

/**
 * SEO Utility Types
 * 
 * Helper types for working with SEO data in a type-safe
 * manner throughout the application and CMS providers.
 */

export type SEODataByType<T extends ContentType> = T extends 'blog'
  ? BlogPostingStructuredData
  : T extends 'product'
  ? ProductStructuredData
  : ArticleStructuredData

export interface SEOAnalytics {
  contentId: string
  type: ContentType
  locale: string
  seoScore: number
  issues: string[]
  recommendations: string[]
}

export interface SEOReport {
  generatedAt: string
  totalContent: number
  byType: { [K in ContentType]: number }
  byLocale: { [locale: string]: number }
  analytics: SEOAnalytics[]
  summary: {
    averageScore: number
    totalIssues: number
    topIssues: string[]
  }
}
