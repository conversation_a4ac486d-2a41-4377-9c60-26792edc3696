/**
 * Content Queries Service
 * 
 * This service handles all content-related data queries, including checking
 * content existence and retrieving content metadata. It serves as the primary
 * interface between the application and the content data source.
 * 
 * Key Features:
 * - Content existence checking across all supported languages
 * - Content title retrieval for display purposes
 * - Type-safe content access with error handling
 * 
 * FUTURE EXTENSIBILITY - Headless CMS Integration:
 * This service is the primary candidate for the adapter pattern. When migrating
 * to a Headless CMS, these functions would be replaced with API calls while
 * maintaining the same interface contracts.
 * 
 * Migration Strategy:
 * - Replace direct Contentlayer imports with adapter calls
 * - Add caching layer for API responses
 * - Implement error handling for network requests
 * - Maintain the same function signatures for backward compatibility
 */

import type { ContentType } from './types'

// Unified Content Service Integration
//
// This service now uses the unified content service which provides a clean
// abstraction layer over different CMS backends. The unified service handles
// provider initialization, type conversion, and consistent API interfaces.
import { unifiedContentService } from '@/services/unified-content'

/**
 * Check if content exists in a specific language
 * 
 * This function queries the Contentlayer generated data to determine if a specific
 * piece of content (identified by type and slug) has a translation available in
 * the target language. It's used to determine language availability before
 * attempting navigation.
 * 
 * FUTURE CMS MIGRATION:
 * This function is a prime candidate for the adapter pattern. When migrating to
 * a Headless CMS, this would become:
 * ```typescript
 * return await contentAdapter.contentExists(contentType, slug, locale)
 * ```
 * The adapter would handle API calls, caching, and data transformation while
 * maintaining the same boolean return type.
 * 
 * @param contentType - Type of content to check ('blog', 'product', 'case-study')
 * @param slug - Unique identifier for the content
 * @param locale - Target locale to check (e.g., 'en', 'zh')
 * @returns true if content exists in the target locale, false otherwise
 */
export async function contentExistsInLocale(
  contentType: ContentType,
  slug: string,
  locale: string
): Promise<boolean> {
  // Early return for non-content types or missing slugs
  if (contentType === 'other' || !slug) {
    return false
  }

  try {
    // Use unified content service to check content existence
    return await unifiedContentService.contentExists(contentType, slug, locale)
  } catch (error) {
    console.error(`Error checking content existence: ${contentType}/${locale}/${slug}`, error)
    return false
  }
}

/**
 * Get content title for display purposes
 * 
 * This function retrieves the actual title of a content item in a specific language
 * for display in UI components. It's useful for showing users what content they're
 * viewing or what content is available in different languages.
 * 
 * The function queries the appropriate Contentlayer collection to find the content
 * item and extract its title. This provides a user-friendly way to identify content
 * across different languages.
 * 
 * FUTURE CMS MIGRATION:
 * This function demonstrates the clean abstraction that makes CMS migration easier.
 * The interface (input parameters and return type) would remain identical, but the
 * implementation would change to:
 * ```typescript
 * return await contentAdapter.getContentTitle(contentType, slug, locale)
 * ```
 * This ensures UI components continue working without modification.
 * 
 * @param contentType - Type of content ('blog', 'product', 'case-study')
 * @param slug - Unique identifier for the content
 * @param locale - Language locale for the content
 * @returns Content title string or null if not found
 */
export async function getContentTitle(
  contentType: ContentType,
  slug: string,
  locale: string
): Promise<string | null> {
  // Early return for non-content types or missing slugs
  if (contentType === 'other' || !slug) {
    return null
  }

  try {
    // Use unified content service to get content title
    return await unifiedContentService.getContentTitle(contentType, slug, locale)
  } catch (error) {
    console.error(`Error fetching content title: ${contentType}/${locale}/${slug}`, error)
    return null
  }
}
