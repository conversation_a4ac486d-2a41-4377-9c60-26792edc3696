/**
 * Content Services - Unified Export
 *
 * This module provides a unified interface to all content-related services.
 * It serves as the main entry point for content management functionality,
 * making it easy to import and use content services throughout the application.
 *
 * Services Architecture:
 * - content-detection.ts: URL parsing and content type detection
 * - content-queries.ts: Content existence and metadata retrieval
 * - language-switching.ts: Intelligent language switching logic
 * - url-generation.ts: URL generation utilities
 * - types.ts: Shared type definitions
 *
 * Usage:
 * ```typescript
 * import {
 *   detectContentPage,
 *   handleContentLanguageSwitch,
 *   getAvailableLanguageVersions
 * } from '@/services/content'
 * ```
 *
 * FUTURE EXTENSIBILITY:
 * This unified export makes it easy to refactor internal implementations
 * without affecting consuming code. When migrating to a Headless CMS,
 * only the service implementations need to change, not the imports.
 */

// Type definitions
export type {
  ContentType,
  ContentPageInfo,
  LanguageVersion,
  LanguageSwitchResult
} from './types'

// Content detection services
export {
  detectContentPage,
  getContentBasePath
} from './content-detection'

// Content query services
export {
  contentExistsInLocale,
  getContentTitle
} from './content-queries'

// URL generation services
export {
  generateContentUrl,
  generateCanonicalUrl,
  generateAlternateUrls
} from './url-generation'

// Language switching services
export {
  getAvailableLanguageVersions,
  handleContentLanguageSwitch,
  shouldShowLanguageSwitching
} from './language-switching'

// Import all functions for the ContentServices object
import { detectContentPage, getContentBasePath } from './content-detection'
import { contentExistsInLocale, getContentTitle } from './content-queries'
import { generateContentUrl, generateCanonicalUrl, generateAlternateUrls } from './url-generation'
import { getAvailableLanguageVersions, handleContentLanguageSwitch, shouldShowLanguageSwitching } from './language-switching'

/**
 * Content Service Utilities
 *
 * This object provides a convenient way to access all content services
 * in a single import, useful for dependency injection or when you need
 * multiple services in one place.
 */
export const ContentServices = {
  // Detection
  detectContentPage,
  getContentBasePath,

  // Queries
  contentExistsInLocale,
  getContentTitle,

  // URL Generation
  generateContentUrl,
  generateCanonicalUrl,
  generateAlternateUrls,

  // Language Switching
  getAvailableLanguageVersions,
  handleContentLanguageSwitch,
  shouldShowLanguageSwitching
} as const

/**
 * Default export for convenience
 *
 * Provides access to all services through a single default import:
 * ```typescript
 * import ContentService from '@/services/content'
 * ContentService.detectContentPage(pathname, locale)
 * ```
 */
export default ContentServices
