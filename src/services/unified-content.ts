/**
 * Unified Content Service
 * 
 * This service provides a unified interface that combines the functionality
 * of both the CMS abstraction layer and the legacy content services.
 * It serves as the single entry point for all content operations throughout
 * the application, ensuring consistency and ease of migration.
 * 
 * Key Features:
 * - Provider-agnostic content operations
 * - Static generation support for Next.js
 * - Language switching and detection
 * - URL generation and routing utilities
 * - Content existence checking
 * - SEO metadata extraction
 * 
 * Migration Strategy:
 * This service gradually replaces direct contentlayer2 imports and
 * legacy content service calls, providing a smooth transition path
 * to the new CMS abstraction architecture.
 */

import { cms, initializeCMS } from '@/cms/services'
import type { 
  ContentType, 
  ContentItem, 
  QueryOptions,
  BlogContent,
  ProductContent,
  CaseStudyContent
} from '@/cms/types'

/**
 * Content Page Information Interface
 * 
 * Represents information about a content page extracted from URL analysis.
 * This is used for routing and language switching functionality.
 */
export interface ContentPageInfo {
  contentType: ContentType
  slug: string
  isContentPage: boolean
}

/**
 * Language Switch Result Interface
 * 
 * Represents the result of a language switching operation,
 * including the target URL and switching strategy used.
 */
export interface LanguageSwitchResult {
  url: string
  strategy: 'direct' | 'fallback' | 'home'
  available: boolean
}

/**
 * Language Version Interface
 * 
 * Represents different language versions of the same content.
 */
export interface LanguageVersion {
  lang: string
  title: string
  url: string
  available: boolean
}

/**
 * Unified Content Service Class
 * 
 * Main service class that provides all content-related functionality
 * through a single, consistent interface. This class coordinates
 * between the CMS abstraction layer and legacy services.
 */
class UnifiedContentService {
  private initialized = false

  /**
   * Initialize the content service
   * 
   * Sets up the CMS backend and prepares the service for content operations.
   * This method should be called once during application startup.
   */
  async initialize(): Promise<void> {
    if (this.initialized) return

    await initializeCMS({
      provider: 'contentlayer2',
      contentTypes: ['blog', 'product', 'case-study'],
      defaultLocale: 'en',
      supportedLocales: ['en', 'zh'],
      features: {
        cache: true,
        seo: true,
        relatedContent: true,
        languageSwitching: true
      }
    })

    this.initialized = true
  }

  /**
   * Ensure service is initialized
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initialize()
    }
  }

  // ==========================================
  // Static Generation Support
  // ==========================================

  /**
   * Get content for static generation
   * 
   * Retrieves all content items of a specific type for Next.js static generation.
   * This method is optimized for build-time usage.
   */
  async getContentForStaticGeneration<T extends ContentItem>(
    type: ContentType
  ): Promise<T[]> {
    await this.ensureInitialized()
    return cms.getContentForStaticGeneration<T>(type)
  }

  /**
   * Get all content slugs for static generation
   * 
   * Retrieves all slug and locale combinations for generateStaticParams.
   */
  async getAllContentSlugs(
    type: ContentType
  ): Promise<Array<{ locale: string; slug: string }>> {
    await this.ensureInitialized()
    return cms.getAllContentSlugs(type)
  }

  // ==========================================
  // Content Queries
  // ==========================================

  /**
   * Get single content item
   */
  async getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null> {
    await this.ensureInitialized()
    return cms.getContent<T>(type, slug, locale)
  }

  /**
   * Get content list with filtering and sorting
   */
  async getContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options?: QueryOptions
  ): Promise<T[]> {
    await this.ensureInitialized()
    return cms.getContentList<T>(type, locale, options)
  }

  /**
   * Check if content exists in a specific language
   */
  async contentExists(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<boolean> {
    await this.ensureInitialized()
    return cms.contentExists(type, slug, locale)
  }

  /**
   * Get content title
   */
  async getContentTitle(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<string | null> {
    await this.ensureInitialized()
    return cms.getContentTitle(type, slug, locale)
  }

  // ==========================================
  // URL and Routing Utilities
  // ==========================================

  /**
   * Detect content page from URL pathname
   * 
   * Analyzes a URL pathname to determine if it represents a content page
   * and extracts the content type and slug information.
   */
  detectContentPage(pathname: string, locale: string): ContentPageInfo {
    // Remove locale prefix if present
    const cleanPath = pathname.replace(new RegExp(`^/${locale}`), '') || '/'
    
    // Content page patterns: /blogs/slug, /products/slug, /case-studies/slug
    const contentPatterns = {
      '/blogs/': 'blog' as ContentType,
      '/products/': 'product' as ContentType,
      '/case-studies/': 'case-study' as ContentType
    }

    for (const [pattern, contentType] of Object.entries(contentPatterns)) {
      if (cleanPath.startsWith(pattern)) {
        const slug = cleanPath.replace(pattern, '').split('/')[0]
        if (slug) {
          return {
            contentType,
            slug,
            isContentPage: true
          }
        }
      }
    }

    return {
      contentType: 'blog', // default
      slug: '',
      isContentPage: false
    }
  }

  /**
   * Generate content URL
   */
  generateContentUrl(
    type: ContentType,
    slug: string,
    locale: string,
    baseUrl: string = ''
  ): string {
    const typeMap = {
      blog: 'blogs',
      product: 'products',
      'case-study': 'case-studies'
    }

    const typePath = typeMap[type]
    const localePath = locale === 'en' ? '' : `/${locale}`
    
    return `${baseUrl}${localePath}/${typePath}/${slug}`
  }

  // ==========================================
  // Language Switching
  // ==========================================

  /**
   * Get available language versions of content
   */
  async getAvailableLanguageVersions(
    type: ContentType,
    slug: string
  ): Promise<LanguageVersion[]> {
    await this.ensureInitialized()
    return cms.getAvailableLanguages(type, slug)
  }

  /**
   * Handle content language switching
   */
  async handleContentLanguageSwitch(
    currentPath: string,
    currentLocale: string,
    targetLocale: string,
    baseUrl: string = ''
  ): Promise<LanguageSwitchResult> {
    const pageInfo = this.detectContentPage(currentPath, currentLocale)
    
    if (!pageInfo.isContentPage) {
      // Not a content page, use simple locale switching
      const newPath = currentPath.replace(new RegExp(`^/${currentLocale}`), `/${targetLocale}`)
      return {
        url: `${baseUrl}${newPath}`,
        strategy: 'direct',
        available: true
      }
    }

    // Check if target language version exists
    const exists = await this.contentExists(pageInfo.contentType, pageInfo.slug, targetLocale)
    
    if (exists) {
      // Direct switch to target language version
      const url = this.generateContentUrl(pageInfo.contentType, pageInfo.slug, targetLocale, baseUrl)
      return {
        url,
        strategy: 'direct',
        available: true
      }
    } else {
      // Fallback to content list page
      const typeMap = {
        blog: 'blogs',
        product: 'products',
        'case-study': 'case-studies'
      }
      const listPath = targetLocale === 'en' ? `/${typeMap[pageInfo.contentType]}` : `/${targetLocale}/${typeMap[pageInfo.contentType]}`
      
      return {
        url: `${baseUrl}${listPath}`,
        strategy: 'fallback',
        available: false
      }
    }
  }
}

// Create and export singleton instance
export const unifiedContentService = new UnifiedContentService()

// Export convenience functions
export const {
  getContentForStaticGeneration,
  getAllContentSlugs,
  getContent,
  getContentList,
  contentExists,
  getContentTitle,
  detectContentPage,
  generateContentUrl,
  getAvailableLanguageVersions,
  handleContentLanguageSwitch
} = unifiedContentService

// Export types
export type {
  ContentType,
  ContentItem,
  BlogContent,
  ProductContent,
  CaseStudyContent,
  QueryOptions
}

export default unifiedContentService
